---
type: "always_apply"
---

# VP Banking Flutter Project Rules

## 📁 Common Libraries & Design System

### 🎨 VP Design System (`library/vp_design_system`)
- **ALWAYS** use design system components instead of creating custom widgets
- Import: `import 'package:vp_design_system/vp_design_system.dart';`
- Available components:
  - `VPScaffold` - Standard app scaffold
  - `VPTabBar` - Consistent tab navigation  
  - `HeaderWidget` - Standard page headers
  - `VPButton` variants - All button styles
  - `InputView` - Text inputs with validation
  - `VPBottomAction` - Bottom action bars

### 🔧 VP Common (`library/vp_common`)
- **REQUIRED** for all feature modules
- Import: `import 'package:vp_common/vp_common.dart';`
- Provides:
  - Extensions (String, Date, Number formatting)
  - App utilities & validators
  - Navigation helpers
  - Shared constants
  - Error handling

### ⚡ VP Core (`library/vp_core`)
- **BASE** for all business logic
- Import: `import 'package:vp_core/vp_core.dart';`
- Contains:
  - BLoC/Cubit base classes
  - API interceptors
  - Theme management
  - Dependency injection setup

## 🏗️ Feature Module Structure
```
features/vp_[feature_name]/
├── lib/
│   ├── core/
│   │   ├── common/          # Feature-specific common utilities
│   │   ├── repository/      # Data layer
│   │   └── service/         # API services
│   ├── model/              # Data models
│   ├── screen/             # UI screens
│   └── router/             # Navigation routes
```

## 📝 Coding Standards

### 🎨 Colors & Text Styles

#### Colors Usage
- **MANDATORY**: Use VP design system color tokens only
- **Access**: `vpColor.[colorName]` or your design system's color constants
- **Examples**:
  ```dart
  // ✅ Correct - Use VP design system colors
  Container(color: vpColor.primary)
  Text('Hello', style: TextStyle(color: vpColor.gray700))
  
  // ❌ Wrong - Never hardcode colors
  Container(color: Colors.blue)
  Text('Hello', style: TextStyle(color: Color(0xFF123456)))
  ```

#### Text Styles Usage
- **MANDATORY**: Use VP design system text styles only
- **Access**: `context.textStyle.[styleName]` (e.g., `context.textStyle.body14`)
- **Available styles**: Use your VP design system text style naming convention
- **Examples**:
  ```dart
  // ✅ Correct - Use VP design system text styles
  Text('Title', style: context.textStyle.heading20)
  Text('Body', style: context.textStyle.body14)
  
  // ❌ Wrong - Never create custom text styles
  Text('Title', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold))
  ```

#### Custom Styling Rules
- **Modifications**: Only modify existing VP text styles, never create from scratch
- **Color Override**: Use `.copyWith(color: vpColor.[colorName])`
- **Examples**:
  ```dart
  // ✅ Correct - Modifying existing VP style
  Text(
    'Error text',
    style: context.textStyle.body14?.copyWith(
      color: vpColor.error
    )
  )
  
  // ❌ Wrong - Creating custom style
  Text(
    'Error text',
    style: TextStyle(
      fontSize: 14,
      color: Colors.red,
      fontFamily: 'Roboto'
    )
  )
  ```

### Common Components Usage
1. **Widgets**: Use design system components first, feature common second
2. **Utils**: Feature common for feature-specific, vp_common for shared
3. **Assets**: Store in `vp_assets` for shared, feature assets for specific
4. **Strings**: Use generated l10n files, avoid hardcoded strings
5. **Colors**: Use design system color tokens exclusively
6. **Text Styles**: Use design system text theme exclusively

### Import Order
```dart
// 1. Flutter/Dart packages
import 'package:flutter/material.dart';

// 2. VP libraries (core first, then common, then design system)
import 'package:vp_core/vp_core.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_design_system/vp_design_system.dart';

// 3. Feature imports
import 'package:vp_bond/model/...';
```

### Naming Conventions
- Screens: `[FeatureName]Screen` (e.g., `BondContractScreen`)
- Common widgets: `[Purpose]Widget` (e.g., `HeaderWidget`)
- Utilities: `[Function]Utils` (e.g., `FormatUtils`)
- Enums: `[Context]Enum` (e.g., `StatusEnum`)

## 🚫 Don'ts
- ❌ Don't create custom widgets if design system has equivalent
- ❌ Don't duplicate utilities between features (move to vp_common)
- ❌ Don't hardcode colors/styles (use design system tokens)
- ❌ Don't import feature modules into common libraries
- ❌ Don't bypass validation utilities from vp_common
- ❌ Don't use `Colors.*` directly (e.g., `Colors.blue`, `Colors.red`)
- ❌ Don't use `Theme.of(context).colorScheme.*` (use vpColor instead)
- ❌ Don't use `Theme.of(context).textTheme.*` (use context.textStyle instead)
- ❌ Don't create `TextStyle()` from scratch
- ❌ Don't use hex colors (e.g., `Color(0xFF123456)`)
- ❌ Don't use custom font sizes/weights outside of VP text styles
- ❌ Don't use `Color.fromRGBO()` or `Color.fromARGB()`

## ✅ Do's
- ✅ Use `S.current.[key]` for localized strings
- ✅ Use `context.pop()` instead of `Navigator.pop()`
- ✅ Follow the established folder structure
- ✅ Leverage design system for consistent UI
- ✅ Use common extensions for data formatting
- ✅ Use `vpColor.*` for all colors
- ✅ Use `context.textStyle.*` for all text styles (e.g., `context.textStyle.body14`)
- ✅ Use `.copyWith()` when modifying existing VP styles
- ✅ Refer to VP design system documentation for available tokens
- ✅ Test color contrast and accessibility with VP design tokens