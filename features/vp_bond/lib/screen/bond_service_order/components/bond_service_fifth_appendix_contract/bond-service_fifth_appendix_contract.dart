import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_bond/core/common/widget/bond_loading_widget.dart';
import 'package:vp_bond/core/common/widget/error_widget.dart';
import 'package:vp_bond/generated/l10n.dart';
import 'package:vp_bond/screen/bond_service_order/components/bond_service_fifth_appendix_contract/bond_service_fifth_appendix_contract_bloc.dart';
import 'package:vp_bond/screen/bond_service_order/components/bond_service_fifth_appendix_contract/bond_service_fifth_appendix_contract_state.dart';
import 'package:vp_common/utils/app_keyboard_utils.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/themes/vp_color_old.dart';

class BondServiceFifthAppendixContractPage extends StatefulWidget {
  const BondServiceFifthAppendixContractPage({Key? key}) : super(key: key);

  @override
  State<BondServiceFifthAppendixContractPage> createState() =>
      _BondFifthAppendixContractPageState();
}

class _BondFifthAppendixContractPageState
    extends State<BondServiceFifthAppendixContractPage>
    with AutomaticKeepAliveClientMixin {
  final Duration animationDuration = const Duration(milliseconds: 200);

  final double itemHeight = 40;
  late final BondServiceFifthAppendixContractBloc _bloc;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _bloc = context.read<BondServiceFifthAppendixContractBloc>();
    AppKeyboardUtils.dismissKeyboard();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      children: [
        Expanded(
          child: LayoutBuilder(
            builder: (cxt, constraint) {
              return BlocBuilder<
                BondServiceFifthAppendixContractBloc,
                BondServiceFifthAppendixContractState
              >(
                bloc: _bloc,
                builder: (cxt, state) {
                  if (state is BondFifthAppendixGetListContractLoading) {
                    return const BondLoadingWidget();
                  }
                  final listContract =
                      context
                          .read<BondServiceFifthAppendixContractBloc>()
                          .listContract;
                  if (listContract != null &&
                      listContract.isNotEmpty &&
                      _bloc.listContract?.first.fileUrl != null) {
                    return SizedBox(
                      height:
                          constraint.maxHeight -
                          listContract.length * itemHeight,
                      child: SfPdfViewer.network(
                        _bloc.listContract?.first.fileUrl ?? '',
                        onDocumentLoaded: (value) {},
                        onDocumentLoadFailed: (value) {},
                        canShowScrollHead: false,
                      ),
                    );

                    // return ListView.separated(
                    //   physics: const NeverScrollableScrollPhysics(),
                    //   separatorBuilder: (cxt, index) {
                    //     return Divider(
                    //       color: ColorUtils.divider,
                    //       height: 1,
                    //     );
                    //   },
                    //   itemCount: listContract.length,
                    //   itemBuilder: (cxt, index) {
                    //     final contractItem = listContract[index];
                    //     return Column(
                    //       mainAxisSize: MainAxisSize.min,
                    //       children: [
                    //         InkWell(
                    //           child: Container(
                    //             child: Row(
                    //               children: [
                    //                 Expanded(
                    //                     child: Text(
                    //                   contractItem.templateName ??
                    //                       'Hợp đồng 0${index + 1}',
                    //                   style:
                    //                       Theme.of(context).textTheme.subtitle2,
                    //                   textAlign: TextAlign.start,
                    //                   maxLines: 1,
                    //                   overflow: TextOverflow.ellipsis,
                    //                 )),
                    //                 const SizedBox(width: 20),
                    //                 GestureDetector(
                    //                   onTap: () {
                    //                     BondTracking.instance.logEvent(
                    //                         name: EventTracking
                    //                             .bondOrderContractDownload);
                    //                     _bloc.downloadContract(context, index);
                    //                   },
                    //                   child: SvgPicture.asset(
                    //                       BondKeyAssets.icDownloadV2,
                    //                       color: ColorUtils.colorIcon),
                    //                 ),
                    //                 const SizedBox(width: 17),
                    //                 ValueListenableBuilder(
                    //                     valueListenable:
                    //                         _bloc.selectedContractId,
                    //                     builder: (cxt, value, _) {
                    //                       return AnimatedCrossFade(
                    //                           firstChild: SizedBox(
                    //                             width: 13,
                    //                             height: 8,
                    //                             child: RotationTransition(
                    //                               turns:
                    //                                   const AlwaysStoppedAnimation(
                    //                                       180 / 360),
                    //                               child: SvgPicture.asset(
                    //                                 BondKeyAssets.icArrowUpSort,
                    //                                 color: colorApp(
                    //                                     ColorKey.black),
                    //                                 fit: BoxFit.fill,
                    //                               ),
                    //                             ),
                    //                           ),
                    //                           secondChild: SizedBox(
                    //                             width: 13,
                    //                             height: 8,
                    //                             child: SvgPicture.asset(
                    //                               BondKeyAssets.icArrowUpSort,
                    //                               color: colorApp(
                    //                                   ColorKey.primary),
                    //                               fit: BoxFit.fill,
                    //                             ),
                    //                           ),
                    //                           crossFadeState: _bloc
                    //                                       .selectedContractId
                    //                                       .value ==
                    //                                   index
                    //                               ? CrossFadeState.showSecond
                    //                               : CrossFadeState.showFirst,
                    //                           duration: animationDuration);
                    //                     })
                    //               ],
                    //             ),
                    //             padding: const EdgeInsets.symmetric(
                    //                 // vertical: SizeUtils.kSize8,
                    //                 horizontal: SizeUtils.kSize16),
                    //             height: itemHeight,
                    //           ),
                    //           onTap: () {
                    //             if (_bloc.selectedContractId.value != index) {
                    //               _bloc.selectedContractId.value = index;
                    //             } else {
                    //               _bloc.selectedContractId.value = -1;
                    //             }
                    //           },
                    //         ),
                    //         ValueListenableBuilder<int>(
                    //           valueListenable: _bloc.selectedContractId,
                    //           builder: (cxt, value, _) {
                    //             return _bloc.selectedContractId.value == index
                    //                 ? SizedBox(
                    //                     height: constraint.maxHeight -
                    //                         listContract.length * itemHeight,
                    //                     child: SfPdfViewer.network(
                    //                       _bloc.listContract![index].fileUrl!,
                    //                       onDocumentLoaded: (value) {},
                    //                       onDocumentLoadFailed: (value) {},
                    //                       canShowScrollHead: false,
                    //                     ),
                    //                   )
                    //                 : const SizedBox();
                    //           },
                    //         )
                    //       ],
                    //     );
                    //   },
                    // );
                  }
                  return ErrorNetworkWidget(
                    tryAgain: true,
                    onPressed: () => _bloc.init(),
                  );
                },
              );
            },
          ),
        ),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border(top: BorderSide(width: 1, color: themeData.divider)),
          ),
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '${S.current.byPressingButton} ',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyLarge?.copyWith(color: themeData.gray700),
                ),
                TextSpan(
                  text: '"${S.current.next.toLowerCase()}"',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                TextSpan(
                  text: S.current.agreeWithContract,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyLarge?.copyWith(color: themeData.gray700),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
