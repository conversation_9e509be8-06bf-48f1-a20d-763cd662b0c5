import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vp_bond/core/repository/bond_repository.dart';
import 'package:vp_bond/generated/assets.gen.dart';
import 'package:vp_bond/generated/l10n.dart';
import 'package:vp_bond/model/response/bond_service/bond_appendix_model.dart';
import 'package:vp_bond/screen/bond_service_order/bloc/bond_service_order_bloc.dart';
import 'package:vp_bond/screen/bond_service_order/components/bond_service_fifth_appendix_contract/bond_service_fifth_appendix_contract_state.dart';
import 'package:vp_common/download_file_manager/base_download_file_manager.dart';
import 'package:vp_common/download_file_manager/download_file_manager.dart';
import 'package:vp_core/vp_core.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:vp_design_system/custom_widget/app_snackbar_utils.dart';

class BondServiceFifthAppendixContractBloc
    extends Cubit<BondServiceFifthAppendixContractState> {
  final BondRepository _repository = GetIt.instance<BondRepository>();
  bool canNext = true;
  List<BondAppendixModel>? listContract;
  List<String> listUniqueContractId = [];
  Map<String, String?> mapContracts = {};
  final ValueNotifier<int> selectedContractId = ValueNotifier(-1);
  final BondServiceOrderBloc orderBloc;

  BondServiceFifthAppendixContractBloc(
    BondServiceFifthAppendixContractState initialState,
    this.orderBloc,
  ) : super(initialState);

  Future<void> init() async {
    try {
      emit(BondFifthAppendixGetListContractLoading());
      listContract = await _repository.getListAppendix(
        orderBloc.bondModel.symbol!,
      );
      emit(BondFifthAppendixGetListContractState(listContract));
    } catch (e) {
      emit(BondFifthAppendixGetListContractFailed(e));
    }
  }

  void resetData() {
    listContract?.clear();
    listUniqueContractId.clear();
    mapContracts.clear();
    selectedContractId.value = -1;
  }

  void downloadContract(BuildContext context, int index) {
    final String tag = 'download_contract_$index';
    EasyDebounce.debounce(tag, const Duration(milliseconds: 1000), () async {
      try {
        showSnackBar(
          context,
          S.current.downloading,
          asset: Assets.icons.icDownloadV2.path,
        );
        final String contractUniqueId = listUniqueContractId[index];
        final String defaultFileName = 'contract$index';
        String? url;
        final String fileName =
            listContract![index].templateName ?? defaultFileName;
        if (_isContractLoaded(contractUniqueId)) {
          url = mapContracts[contractUniqueId]!;
        } else {
          // url = await getContractUrl(context, index);
        }

        final downloadResult = await DownloadFileManager.instance.downloadFile(
          fileName: fileName,
          url: url!,
          extension: FileExtension.pdf,
        );

        showSnackBar(
          context,
          downloadResult != null
              ? S.current.downloadSuccess
              : S.current.downloadFailed,
          asset:
              downloadResult != null
                  ? Assets.icons.icSuccess.path
                  : Assets.icons.icFail.path,
        );
      } catch (e) {
        showSnackBar(
          context,
          S.current.downloadFailed,
          asset: Assets.icons.icFail.path,
        );
      }
    });
  }

  bool _isContractLoaded(String uniqueItemId) {
    return mapContracts[uniqueItemId] != null;
  }
}
