import 'package:flutter/material.dart';
import 'package:vp_bond/model/response/bond_service/bond_appendix_model.dart';

@immutable
abstract class BondServiceFifthAppendixContractState {}

class BondFifthAppendixContractStateInit
    extends BondServiceFifthAppendixContractState {}

class BondFifthAppendixContractCanNextState
    extends BondServiceFifthAppendixContractState {
  final bool canNext;

  BondFifthAppendixContractCanNextState(this.canNext);
}

class BondFifthAppendixGetListContractState
    extends BondServiceFifthAppendixContractState {
  final List<BondAppendixModel>? listData;

  BondFifthAppendixGetListContractState(this.listData);
}

class BondFifthAppendixGetListContractLoading
    extends BondServiceFifthAppendixContractState {
  BondFifthAppendixGetListContractLoading();
}

class BondFifthAppendixGetListContractFailed
    extends BondServiceFifthAppendixContractState {
  final dynamic error;

  BondFifthAppendixGetListContractFailed(this.error);
}
