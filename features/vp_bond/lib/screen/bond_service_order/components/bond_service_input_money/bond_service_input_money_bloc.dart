import 'package:flutter/cupertino.dart';
import 'package:vp_bond/core/common/utils/check_professional_singleton.dart';
import 'package:vp_bond/core/repository/bond_repository.dart';
import 'package:vp_bond/generated/l10n.dart';
import 'package:vp_bond/model/request/bond_pricing_param.dart';
import 'package:vp_bond/model/response/detail_bond/bond_info_model.dart';
import 'package:vp_bond/screen/bond_service_order/bloc/bond_service_order_bloc.dart';
import 'package:vp_bond/screen/bond_service_order/components/bond_service_input_money/bond_service_input_money_state.dart';
import 'package:vp_common/utils/app_time_utils.dart';
import 'package:vp_core/vp_core.dart';

class BondServiceInputMoneyBloc extends Cubit<BondServiceInputMoneyState> {
  final String regex = ',';
  int? errorCode;

  bool canNext = false;
  String errorMessage = '';

  num price = 0;

  final BondServiceOrderBloc orderBloc;
  final TextEditingController controller = TextEditingController();
  final double suffixUnitWidth = 40;
  final ScrollController scrollController = ScrollController();
  final BondRepository _bondRepository = GetIt.instance<BondRepository>();

  BondServiceInputMoneyBloc(
    BondServiceInputMoneyState initialState,
    this.orderBloc,
  ) : super(initialState);

  Future<void> init() async {}
  String getSaleProductType(BondInvestmentTermModel? item) {
    if (item?.isOutright() ?? false) {
      return 'OUT_RIGHT';
    } else {
      return 'REPO';
    }
  }

  String? getProfit(BondInvestmentTermModel? item) {
    if (item?.isOutright() ?? false) {
      return null;
    } else {
      return item?.investmentTerm ?? '';
    }
  }

  num? getInvestmentInterest(BondInvestmentTermModel? item) {
    return item?.profit ?? 0;
  }

  Future<void> handleCallGetBondPrice() async {
    final item = orderBloc.listOptionBloc.selectedLiquidity;
    try {
      emit(state.copyWith(isLoading: true));
      price = await _bondRepository.getBondPrice(
        BondPricingParam(
          customerType:
              CheckProfessionalSingleton().customerTypeForRequestInSelling,
          transactionDate: AppTimeUtils.getDateTimeString(),
          bondSymbol: orderBloc.bondModel.symbol,
          disbursementCode: orderBloc.bondModel.disbursementCode,
          saleProductType: getSaleProductType(item),
          profit: getProfit(item),
          investmentInterest: getInvestmentInterest(item),
        ),
      );
    } catch (e) {}
    emit(state.copyWith(isLoading: false, price: price));
  }

  num get unitPrice => state.price ?? 0;

  num get quantity => state.quantity;

  num totalInvest() => quantity * unitPrice;

  void updateInputMoney() {
    num quantity = int.tryParse(controller.text.replaceAll(regex, '')) ?? 0;
    emit(state.copyWith(quantity: quantity, isLoading: false));
    canNext = _checkCanNext();
    emit(state.copyWith(message: errorMessage));
  }

  bool _checkCanNext() {
    final validateQuantity = orderBloc.bondModel.quantity ?? 0;
    final minInvestmentValue = orderBloc.bondModel.minInvestmentValue ?? 0;
    final maxInvestmentValue = orderBloc.bondModel.maxInvestmentValue;
    if (validateQuantity == 0) {
      return false;
    }

    if (quantity == 0) {
      errorMessage = S.current.validateQuantity;
      return false;
    } else if (quantity > validateQuantity) {
      errorMessage = S.current.overAmount;
      return false;
    } else if (quantity < minInvestmentValue) {
      errorMessage =
          S.current.minQuantity + ' $minInvestmentValue';
      return false;
    } else if (maxInvestmentValue != null) {
      if (quantity > maxInvestmentValue) {
        errorMessage =
            S.current.maxQuantity + ' $maxInvestmentValue';
        return false;
      }
    }
    return true;
  }
}
