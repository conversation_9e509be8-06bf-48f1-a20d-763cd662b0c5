import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';

/// Helper class để tạo request cho các loại lệnh điều kiện phái sinh
class DerivativeConditionOrderHelper {
  /// Parse price from String to num using priceDerivative extension
  static num? _parsePrice(String? priceString) {
    if (priceString == null || priceString.isEmpty) return null;
    return priceString.priceDerivative;
  }

  /// Tạo request cho Stop Order
  static ConditionOrderRequestModel createStopOrderRequest({
    required ConditionOrderBookModel originalOrder,
    required int newVolume,
    required double newPrice,
    required double newActivePrice,
    ActivationConditionsType? activationType,
  }) {
    return ConditionOrderRequestModel(
      requestId: "app_${AppHelper().genXRequestID()}",
      orderType: originalOrder.orderType ?? '',
      accountId: originalOrder.accountId ?? '',
      orderId: originalOrder.orderId ?? '',
      conditionInfo: ConditionInfo(
        symbol: originalOrder.symbol ?? '',
        qty: newVolume,
        side: originalOrder.side?.toLowerCase() ?? '',
        type: "limit",
        price:
            newPrice.toString().price, // Use price extension for server format
        fromDate: originalOrder.fromDate ?? '',
        toDate: originalOrder.toDate ?? '',
        activePrice:
            newActivePrice
                .toString()
                .price, // Use price extension for server format
        activeType:
            activationType?.toParamRequest() ?? originalOrder.activeType ?? '',
      ),
    );
  }

  /// Tạo request cho Take Profit/Stop Loss
  static ConditionOrderRequestModel createTakeProfitStopLossRequest({
    required ConditionOrderBookModel originalOrder,
    required int newVolume,
    double? newPriceTP,
    double? newPriceSL,
    double? newActivePriceTP,
    double? newActivePriceSL,
  }) {
    return ConditionOrderRequestModel(
      requestId: "app_${AppHelper().genXRequestID()}",
      orderType: originalOrder.orderType ?? '',
      accountId: originalOrder.accountId ?? '',
      orderId: originalOrder.orderId ?? '',
      conditionInfo: ConditionInfo(
        symbol: originalOrder.symbol ?? '',
        qty: newVolume,
        side: originalOrder.side?.toLowerCase() ?? '',
        type: "limit",
        fromDate: originalOrder.fromDate ?? '',
        toDate: originalOrder.toDate ?? '',
        // Take Profit fields
        priceTP:
            newPriceTP != null
                ? newPriceTP.toString().price
                : _parsePrice(originalOrder.priceTP),
        activepriceTP:
            newActivePriceTP != null
                ? newActivePriceTP.toString().price
                : originalOrder.activepriceTP,
        priceTypeTP: originalOrder.priceTypeTP,
        // Stop Loss fields
        priceSL:
            newPriceSL != null
                ? newPriceSL.toString().price
                : _parsePrice(originalOrder.priceSL),
        activepriceSL:
            newActivePriceSL != null
                ? newActivePriceSL.toString().price
                : originalOrder.activepriceSL,
        priceTypeSL: originalOrder.priceTypeSL,
        // Other fields
        timetype: originalOrder.timeTypeValue,
        split: originalOrder.split,
        priceStep: _parsePrice(originalOrder.priceStep),
        deltaValue: originalOrder.deltaValue,
        deltaType: originalOrder.deltaType,
      ),
    );
  }

  /// Tạo request cho Trailing Stop Order
  static ConditionOrderRequestModel createTrailingStopOrderRequest({
    required ConditionOrderBookModel originalOrder,
    required int newVolume,
    required double newActivePrice,
    double? newDeltaValue,
    String? newDeltaType,
  }) {
    return ConditionOrderRequestModel(
      requestId: "app_${AppHelper().genXRequestID()}",
      orderType: originalOrder.orderType ?? '',
      accountId: originalOrder.accountId ?? '',
      orderId: originalOrder.orderId ?? '',
      conditionInfo: ConditionInfo(
        symbol: originalOrder.symbol ?? '',
        qty: newVolume,
        side: originalOrder.side?.toLowerCase() ?? '',
        type: "limit",
        fromDate: originalOrder.fromDate ?? '',
        toDate: originalOrder.toDate ?? '',
        activePrice:
            newActivePrice
                .toString()
                .price, // Use price extension for server format
        activeType: originalOrder.activeType ?? '',
        deltaValue: newDeltaValue ?? originalOrder.deltaValue,
        deltaType: newDeltaType ?? originalOrder.deltaType,
        priceStep: _parsePrice(originalOrder.priceStep),
      ),
    );
  }

  /// Tạo request chung cho TPSL
  static ConditionOrderRequestModel createTPSLRequest({
    required ConditionOrderBookModel originalOrder,
    required int newVolume,
    double? newPriceTP,
    double? newPriceSL,
    double? newActivePriceTP,
    double? newActivePriceSL,
    String? newPriceTypeTP,
    String? newPriceTypeSL,
  }) {
    return ConditionOrderRequestModel(
      requestId: "app_${AppHelper().genXRequestID()}",
      orderType: originalOrder.orderType ?? '',
      accountId: originalOrder.accountId ?? '',
      orderId: originalOrder.orderId ?? '',
      conditionInfo: ConditionInfo(
        symbol: originalOrder.symbol ?? '',
        qty: newVolume,
        side: originalOrder.side?.toLowerCase() ?? '',
        type: "limit",
        fromDate: originalOrder.fromDate ?? '',
        toDate: originalOrder.toDate ?? '',
        // Take Profit fields
        priceTP:
            newPriceTP != null
                ? newPriceTP.toString().price
                : _parsePrice(originalOrder.priceTP),
        activepriceTP:
            newActivePriceTP != null
                ? newActivePriceTP.toString().price
                : originalOrder.activepriceTP,
        priceTypeTP: newPriceTypeTP ?? originalOrder.priceTypeTP,
        // Stop Loss fields
        priceSL:
            newPriceSL != null
                ? newPriceSL.toString().price
                : _parsePrice(originalOrder.priceSL),
        activepriceSL:
            newActivePriceSL != null
                ? newActivePriceSL.toString().price
                : originalOrder.activepriceSL,
        priceTypeSL: newPriceTypeSL ?? originalOrder.priceTypeSL,
        // Other fields
        timetype: originalOrder.timeTypeValue,
        split: originalOrder.split,
        priceStep: _parsePrice(originalOrder.priceStep),
        deltaValue: originalOrder.deltaValue,
        deltaType: originalOrder.deltaType,
      ),
    );
  }

 
}
