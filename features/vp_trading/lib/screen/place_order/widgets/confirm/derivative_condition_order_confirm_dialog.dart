import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/utils/app_launching_utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/constant/order_type.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';
import 'package:vp_trading/screen/place_order/widgets/confirm/widget/order_popup_note_widget.dart';

class DerivativeConditionOrderConfirmDialog extends StatelessWidget {
  final DerivativeOrderOptionalType orderType;

  final Function(bool showConfirmOrder) onConfirm;

  final ConditionOrderRequestModel requestModel;

  const DerivativeConditionOrderConfirmDialog({
    super.key,
    required this.requestModel,
    required this.onConfirm,
    required this.orderType,
  });

  @override
  Widget build(BuildContext context) {
    bool isBuyOrder = requestModel.conditionInfo.side == OrderAction.buy.value;

    Color orderColor = isBuyOrder ? themeData.primary : themeData.red;

    String orderLabel =
        isBuyOrder
            ? OrderAction.buy.nameTypeDerivative
            : OrderAction.sell.nameTypeDerivative;

    String symbol = requestModel.conditionInfo.symbol;

    String title = VPTradingLocalize.current.derivative_order_accept;
    String price = requestModel.conditionInfo.price.toString();
    String priceStep =
        (requestModel.conditionInfo.priceStep ?? 0).priceInt.toString();
    String deltaValue =
        (requestModel.conditionInfo.deltaValue ?? 0).priceInt.toString();
    String priceTP = requestModel.conditionInfo.priceTP.toString();
    String priceSL = requestModel.conditionInfo.priceSL.toString();
    String activePriceSL = requestModel.conditionInfo.activepriceSL.toString();
    String activePrice = requestModel.conditionInfo.activePrice.toString();
    String activeType = requestModel.conditionInfo.activeType ?? '';
    String activeTypeSymbol =
        activeType == ActivationConditionsType.greaterThan.toParamRequest()
            ? ActivationConditionsType.greaterThan.getSymbol()
            : ActivationConditionsType.lessThan.getSymbol();

    String buttonLabel = VPCommonLocalize.current.confirm;

    bool showConfirmOrder = true;
    final List<String> contents =
        'Bằng việc %&Xác nhận lệnh%&, bạn hiểu và chấp thuận với các lệnh giao dịch  được phát sinh khi thỏa mãn các điều kiện kích hoạt. Quý khách vui lòng đọc rõ về nguyên tắc hoạt động của các loại lệnh điều kiện'
            .split('%&');

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SvgPicture.asset(
          VpTradingAssets.icons.icOrder.path,
          package: 'vp_trading',
          colorFilter: ColorFilter.mode(orderColor, BlendMode.srcIn),
        ),

        const SizedBox(height: 32),
        Text(
          title,
          style: vpTextStyle.headineBold6.copyColor(themeData.displayLarge),
        ),
        const SizedBox(height: 16),
        OrderConfirmRowTitle(
          title: VPTradingLocalize.current.derivative_order_type,
          content: orderLabel,
        ),
        const SizedBox(height: 8),
        OrderConfirmRowTitle(
          title: VPTradingLocalize.current.derivative_contract_code,
          content: symbol,
        ),
        const SizedBox(height: 8),
        OrderConfirmRowTitle(
          title: VPTradingLocalize.current.derivative_activation_conditions,
          content: orderType.toNameDialog,
        ),
        const SizedBox(height: 8),
        OrderConfirmRowTitle(
          title: VPTradingLocalize.current.trading_volume,
          content: requestModel.conditionInfo.qty.volumeString,
        ),
        const SizedBox(height: 8),
        if (orderType.isStopOrder) ...[
          OrderConfirmRowTitle(
            title: VPTradingLocalize.current.trading_order_price,
            content: price,
          ),
          const SizedBox(height: 8),
          OrderConfirmRowTitle(
            title: VPTradingLocalize.current.derivative_active_price,
            content: "$activeTypeSymbol $activePrice",
          ),
        ],
        if (orderType.isTrailingStop) ...[
          OrderConfirmRowTitle(
            title: VPTradingLocalize.current.derivative_step_price,
            content: priceStep,
          ),
          const SizedBox(height: 8),
          OrderConfirmRowTitle(
            title: VPTradingLocalize.current.derivative_range,
            content: deltaValue,
          ),
        ],
        if (orderType.isStopLossOrTakeProfit) ...[
          OrderConfirmRowTitle(
            title: VPTradingLocalize.current.trading_order_price,
            content: price,
          ),
          const SizedBox(height: 8),
          OrderConfirmRowTitle(
            title: VPTradingLocalize.current.trading_take_profit,
            content: priceTP,
          ),
          const SizedBox(height: 8),
          OrderConfirmRowTitle(
            title: VPTradingLocalize.current.trading_stop_loss,
            content: priceSL,
          ),
          const SizedBox(height: 8),
          OrderConfirmRowTitle(
            title: VPTradingLocalize.current.trading_condition_stop_loss,
            content: activePriceSL,
          ),
        ],

        const SizedBox(height: 8),
        const OrderPopupNote(),
        const SizedBox(height: 16),
        RichText(
          text: TextSpan(
            style: vpTextStyle.captionMedium.copyColor(themeData.gray500),
            children: <TextSpan>[
              TextSpan(text: contents.getElementAt(0) ?? ""),
              TextSpan(
                text: contents.getElementAt(1) ?? "",
                style: vpTextStyle.captionMedium.copyColor(themeData.black),
              ),
              TextSpan(text: contents.getElementAt(2) ?? ""),
              TextSpan(
                text: " tại đây",
                style: vpTextStyle.captionMedium.copyColor(themeData.primary),
                recognizer:
                    TapGestureRecognizer()
                      ..onTap = () {
                        openBrowser(conditionalDerivativeOrderGuideUrl);
                      },
              ),
              const TextSpan(text: "."),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: VpsButton.secondarySmall(
                title: VPCommonLocalize.current.close,
                onPressed: () => Navigator.pop(context),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child:
                  isBuyOrder
                      ? VpsButton.primarySmall(
                        title: buttonLabel,
                        onPressed: () {
                          Navigator.pop(context);
                          onConfirm(showConfirmOrder);
                        },
                      )
                      : VpsButton.primaryDangerSmall(
                        title: buttonLabel,
                        onPressed: () {
                          Navigator.pop(context);
                          onConfirm(showConfirmOrder);
                        },
                      ),
            ),
          ],
        ),
      ],
    );
  }
}

class OrderConfirmRowTitle extends StatelessWidget {
  final String title;
  final String content;

  const OrderConfirmRowTitle({
    super.key,
    required this.title,
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title, style: vpTextStyle.body14.copyColor(themeData.gray700)),
        Text(
          content,
          style: vpTextStyle.body14.copyColor(themeData.displayLarge),
          textAlign: TextAlign.end,
        ),
      ],
    );
  }
}
