import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_trading/cubit/place_order/stock_info/stock_info_cubit.dart';

class StockVolumeValueView extends StatelessWidget {
  const StockVolumeValueView({super.key});

  Widget buildVolumeView(num? volume) {
    return Row(
      children: [
        Text(
          'KLGD  ',
          style: vpTextStyle.captionMedium.copyColor(vpColor.textTertiary),
        ),
        Text(
          volume != null ? FormatUtils.formatVol(volume) : '-',
          style: vpTextStyle.captionMedium.copyColor(vpColor.textPrimary),
        ),
      ],
    );
  }

  Widget buildValueView(num? value) {
    return Row(
      children: [
        Text(
          'GTGD  ',
          style: vpTextStyle.captionMedium.copyColor(vpColor.textTertiary),
        ),
        Text(
          value != null ? FormatUtils.formatVolWithTrailing(value) : '-',
          style: vpTextStyle.captionMedium.copyColor(vpColor.textPrimary),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 2,
      children: [
        BlocSelector<StockInfoCubit, StockInfoState, StockInfoModel?>(
          selector: (state) => state.stockInfo,
          builder: (context, stock) {
            if (stock == null) return buildVolumeView(null);

            return VPTotalTradingVolumeItemView(
              symbol: stock.symbol,
              initTotalVolume: stock.totalTradingVolume,
              builder: (volume) => buildVolumeView(volume),
            );
          },
        ),

        BlocSelector<StockInfoCubit, StockInfoState, StockInfoModel?>(
          selector: (state) => state.stockInfo,
          builder: (context, stock) {
            if (stock == null) return buildValueView(null);

            return VPTotalTradingValueItemView(
              symbol: stock.symbol,
              initTotalValue: stock.totalTradingValue,
              builder: (value) => buildValueView(value),
            );
          },
        ),
      ],
    );
  }
}
