import 'package:vp_common/vp_common.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_utility/core/base/base_decoder.dart';
import 'package:vp_utility/core/base/base_response/base_response.dart';
import 'package:vp_utility/core/constant/derivative_path_api.dart';
import 'package:vp_utility/core/repository/derivative_statement/derivative_statement_repository.dart';
import 'package:vp_utility/screen/derivative_statement/data/params/derivative_statement_of_money_param.dart';
import 'package:vp_utility/screen/derivative_statement/models/derivative_statement_model.dart';

class DerivativeStatementRepositoryImpl
    implements DerivativeStatementRepository {
  final Dio _restClient;

  DerivativeStatementRepositoryImpl({required Dio restClient})
    : _restClient = restClient;

  @override
  Future<BaseDecoder<List<DerivativeStatementModel>>> getListStatement({
    required DerivativeStatementParam param,
  }) async {
    try {
      final response = await _restClient.get(
        DerivativePathApi.getListDerivativeStatement
            .replaceAll("{accountId}", param.accountId)
            .replaceAll("{pageIndex}", "${param.pageIndex}")
            .replaceAll("{pageSize}", "${param.pageSize}")
            .replaceAll("{fromDate}", param.fromDate)
            .replaceAll("{toDate}", param.toDate)
            .replaceAll(
              "{tltxCd}",
              param.tltxCd?.isNotEmpty == true ? "${param.tltxCd}" : "",
            ),
      );
      return BaseDecoder(
        BaseResponseMeta.fromJson(response.data),
        decoder: DerivativeStatementModel.fromListJson,
      );
    } on DioException catch (e) {
      throw HandleError.from<CoreErrorResponse>(e);
    }
  }
}
