class DerivativeStatementModel {
  int? numrows;
  String? afacctno;
  String? txdate;
  String? txnum;
  String? tltxcd;
  String? desc;
  int? credit;
  int? debit;
  int? balance;
  dynamic? createdtime;

  DerivativeStatementModel({
    this.numrows,
    this.afacctno,
    this.txdate,
    this.txnum,
    this.tltxcd,
    this.desc,
    this.credit,
    this.debit,
    this.balance,
    this.createdtime,
  });

  DerivativeStatementModel.fromJson(Map<String, dynamic> json) {
    numrows = json['numrows'];
    afacctno = json['afacctno'];
    txdate = json['txdate'];
    txnum = json['txnum'];
    tltxcd = json['tltxcd'];
    desc = json['desc'];
    credit = json['credit'];
    debit = json['debit'];
    balance = json['balance'];
    createdtime = json['createdtime'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['numrows'] = this.numrows;
    data['afacctno'] = this.afacctno;
    data['txdate'] = this.txdate;
    data['txnum'] = this.txnum;
    data['tltxcd'] = this.tltxcd;
    data['desc'] = this.desc;
    data['credit'] = this.credit;
    data['debit'] = this.debit;
    data['balance'] = this.balance;
    data['createdtime'] = this.createdtime;
    return data;
  }

  static List<DerivativeStatementModel> fromListJson(dynamic json) {
    final list = <DerivativeStatementModel>[];
    final array = json?['content'] ?? [];
    array?.map((v) {
      list.add(DerivativeStatementModel.fromJson(v));
    }).toList();
    return list;
  }
}
