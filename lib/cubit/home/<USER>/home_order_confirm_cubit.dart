import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_utility/core/repository/order_confirm/order_confirm_repository.dart';

part 'home_order_confirm_state.dart';

class HomeOrderConfirmCubit extends Cubit<HomeOrderConfirmState>
    with OrderStatusSocketMixin {
  HomeOrderConfirmCubit() : super(const HomeOrderConfirmState());
  final OrderConfirmRepository _repository =
      GetIt.instance<OrderConfirmRepository>();

  void init() {
    _getOrderConfirm();
    subscribeOrderStatusSocket();
  }

  Future<void> _getOrderConfirm() async {
    try {
      final isShowBtOrderConfirmLate =
          RemoteConfigService().showBtOrderConfirmLate();

      final result = await Future.wait(
        GetIt.instance<SubAccountCubit>().subAccountsStock.map(
              (e) => _repository.confirmOrdersInfor(subAccount: e.id),
            ),
      );

      final orderConfirmCount = result.fold<int>(0, (p, e) => p + e);
      emit(state.copyWith(
        orderConfirmCount: orderConfirmCount,
        isShowBtOrderConfirmLate: isShowBtOrderConfirmLate,
      ));
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  @override
  void onOrderStatusListener(VPOrderStatusData data) {
    if (data.type == 'OD' || data.type == 'SE') _getOrderConfirm();
  }
}
