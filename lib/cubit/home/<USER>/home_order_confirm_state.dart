part of 'home_order_confirm_cubit.dart';

final class HomeOrderConfirmState extends Equatable {
  const HomeOrderConfirmState({
    this.orderConfirmCount = 0,
    this.isShowBtOrderConfirmLate = true,
  });

  final int orderConfirmCount;
  final bool isShowBtOrderConfirmLate;

  @override
  List<Object?> get props => [orderConfirmCount, isShowBtOrderConfirmLate];

  HomeOrderConfirmState copyWith({
    int? orderConfirmCount,
    bool? isShowBtOrderConfirmLate,
  }) {
    return HomeOrderConfirmState(
      orderConfirmCount: orderConfirmCount ?? this.orderConfirmCount,
      isShowBtOrderConfirmLate:
          isShowBtOrderConfirmLate ?? this.isShowBtOrderConfirmLate,
    );
  }
}
