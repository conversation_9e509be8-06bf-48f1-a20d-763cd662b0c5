import 'package:flutter/material.dart';
import 'package:new_neo_invest/core/repository/home_repository.dart';
import 'package:new_neo_invest/generated/l10n.dart';
import 'package:new_neo_invest/main_app_module.dart';
import 'package:new_neo_invest/screen/popup_update/contract_screen.dart';
import 'package:tuple/tuple.dart';
import 'package:vp_auth/utils/loading_utils.dart';
import 'package:vp_centralize/helper/utils.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/app_snackbar_utils.dart';
import 'package:vp_utility/core/repository/order_confirm/order_confirm_repository.dart';

part 'popup_update_state.dart';

class PopupUpdateCubit extends Cubit<PopupUpdateState> {
  PopupUpdateCubit() : super(PopupUpdateInitial());

  HomeRepository get repository => GetIt.instance<HomeRepository>();

  void init() {
    final verificationInfoModel =
        GetIt.instance<AuthCubit>().verificationInfoModel;
    final needChangeIdMode = verificationInfoModel?.needChangeIdMode ?? 0;
    final tc13 = verificationInfoModel?.tcStatus ?? 'NOT_PROCESS';
    final needUpdateContract =
        verificationInfoModel?.needResignContract ?? false;

    if (needChangeIdMode == 1 || needChangeIdMode == 2) {
      emit(Home4501State(needChangeIdMode));
    } else if (tc13 == 'PROCESSING') {
      emit(UpdateTC13State(tc13));
    } else if (needUpdateContract) {
      emit(ResignContract());
    } else {
      emit(NoPopupState());
    }
  }

  void verifyTC13() async {
    try {
      final response = await repository.verifyTC13();

      if (response.isSuccess) {
        showSnackBar(getContext, response.message ?? '');
      } else {
        showErrorMessage(response.message);
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  void onDownloadFile({String? attempUrl, String? fileName}) async {
    if (attempUrl.isNullOrEmpty) return;

    try {
      LoadingUtil.showLoading();

      await DownloadFileManager.instance.downloadFile(
        fileName: fileName,
        extension: FileExtension.pdf,
        url: attempUrl ?? '',
      );
      LoadingUtil.hideLoading();
      await getContext.push(MainRouter.contract.routeName,
          extra: FundContract(attempUrl ?? '', '', '', ''));
    } catch (e) {
      LoadingUtil.hideLoading();
      showError(e);
    }
  }

  void getContractTC13() async {
    try {
      final response = await RemoteConfigService()
          .getRemoteConfigStringValue('contract_tc13');
      if (response.isNotEmpty) {
        onDownloadFile(attempUrl: response, fileName: 'fileND13');
      }
    } catch (e) {
      showError(e);
    }
  }

  Future<void> verifyContractOTP() async {
    LoadingUtil.showLoading();

    final tuple3 = await generalContractOTP();

    LoadingUtil.hideLoading();

    final isSuccess = tuple3.item1;

    final errorMessage = tuple3.item2;

    if (isSuccess) {
      CentralizeUtils().verifySmsOTP(
        onSubmitPinCode: (otp, __, ___, ____) {
          return callApiVerifyContract(otp!);
        },
        onResendSmsOTP: (__, ___) {
          return generalContractOTP();
        },
        onSuccess: (responseApi) {
          showMessage(VPNeoLocalize.current.home_confirmedSuccessfully,
              isSuccess: true);
        },
        onFail: (e) => showError(e),
      );
    } else {
      showErrorMessage(errorMessage);
    }
  }

  Future<Tuple3<bool, String?, Response<dynamic>?>> callApiVerifyContract(
      String otp) async {
    try {
      final response =
          await GetIt.instance<OrderConfirmRepository>().verifyContractOTP(otp);

      return Tuple3(true, null, response);
    } catch (e) {
      final errorMessage = await getErrorMessage(e);

      return Tuple3(false, errorMessage, null);
    }
  }

  Future<Tuple3<bool, String?, Response<dynamic>?>> generalContractOTP() async {
    try {
      await GetIt.instance<OrderConfirmRepository>().generalContractOTP();

      return const Tuple3(true, null, null);
    } catch (e) {
      final errorMessage = await getErrorMessage(e);

      return Tuple3(false, errorMessage, null);
    }
  }
}
