part of 'popup_update_cubit.dart';

@immutable
sealed class PopupUpdateState {
  bool get isInitial => this is PopupUpdateInitial;
}

final class PopupUpdateInitial extends PopupUpdateState {}

class UpdateTC13State extends PopupUpdateState {
  UpdateTC13State(this.tc13Status);

  final String? tc13Status;
}

class Home4501State extends PopupUpdateState {
  Home4501State(this.needChangeIdMode);

  final int? needChangeIdMode;
}

class ResignContract extends PopupUpdateState {}

class NoPopupState extends PopupUpdateState {}

class HomePopupSupperComboState extends PopupUpdateState {
  HomePopupSupperComboState({
    required this.isShow,
    required this.isBlockToday,
    required this.isShowByFirebase,
  });

  final bool isShow;
  final bool isBlockToday;
  final bool isShowByFirebase;

  HomePopupSupperComboState copyWith({
    bool? isShow,
    bool? isBlockToday,
    bool? isShowByFirebase,
  }) {
    return HomePopupSupperComboState(
      isShow: isShow ?? this.isShow,
      isBlockToday: isBlockToday ?? this.isBlockToday,
      isShowByFirebase: isShowByFirebase ?? this.isShowByFirebase,
    );
  }
}
