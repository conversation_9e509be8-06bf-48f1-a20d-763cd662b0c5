/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/clipboardalt.svg
  String get clipboardalt => 'assets/icons/clipboardalt.svg';

  /// File path: assets/icons/dialog_icon_photo.svg
  String get dialogIconPhoto => 'assets/icons/dialog_icon_photo.svg';

  /// File path: assets/icons/dialog_icon_photo_dark.svg
  String get dialogIconPhotoDark => 'assets/icons/dialog_icon_photo_dark.svg';

  /// File path: assets/icons/ic_add_user.svg
  String get icAddUser => 'assets/icons/ic_add_user.svg';

  /// File path: assets/icons/ic_asset.svg
  String get icAsset => 'assets/icons/ic_asset.svg';

  /// File path: assets/icons/ic_bond.svg
  String get icBond => 'assets/icons/ic_bond.svg';

  /// File path: assets/icons/ic_btn_camera.svg
  String get icBtnCamera => 'assets/icons/ic_btn_camera.svg';

  /// File path: assets/icons/ic_camera.svg
  String get icCamera => 'assets/icons/ic_camera.svg';

  /// File path: assets/icons/ic_capture.svg
  String get icCapture => 'assets/icons/ic_capture.svg';

  /// File path: assets/icons/ic_capture_copy.svg
  String get icCaptureCopy => 'assets/icons/ic_capture_copy.svg';

  /// File path: assets/icons/ic_capture_dialog.svg
  String get icCaptureDialog => 'assets/icons/ic_capture_dialog.svg';

  /// File path: assets/icons/ic_change_money.svg
  String get icChangeMoney => 'assets/icons/ic_change_money.svg';

  /// File path: assets/icons/ic_close_game.svg
  String get icCloseGame => 'assets/icons/ic_close_game.svg';

  /// File path: assets/icons/ic_feature_edit.svg
  String get icFeatureEdit => 'assets/icons/ic_feature_edit.svg';

  /// File path: assets/icons/ic_feature_empty.svg
  String get icFeatureEmpty => 'assets/icons/ic_feature_empty.svg';

  /// File path: assets/icons/ic_home_ai.svg
  String get icHomeAi => 'assets/icons/ic_home_ai.svg';

  /// File path: assets/icons/ic_invest.svg
  String get icInvest => 'assets/icons/ic_invest.svg';

  /// File path: assets/icons/ic_member_rank.svg
  String get icMemberRank => 'assets/icons/ic_member_rank.svg';

  /// File path: assets/icons/ic_noti_yellow.svg
  String get icNotiYellow => 'assets/icons/ic_noti_yellow.svg';

  /// File path: assets/icons/ic_notification.svg
  String get icNotification => 'assets/icons/ic_notification.svg';

  /// File path: assets/icons/ic_order_confirm.svg
  String get icOrderConfirm => 'assets/icons/ic_order_confirm.svg';

  /// File path: assets/icons/ic_partner_connection.svg
  String get icPartnerConnection => 'assets/icons/ic_partner_connection.svg';

  /// File path: assets/icons/ic_search.svg
  String get icSearch => 'assets/icons/ic_search.svg';

  /// File path: assets/icons/ic_sec.svg
  String get icSec => 'assets/icons/ic_sec.svg';

  /// File path: assets/icons/ic_security.svg
  String get icSecurity => 'assets/icons/ic_security.svg';

  /// File path: assets/icons/ic_set_command.svg
  String get icSetCommand => 'assets/icons/ic_set_command.svg';

  /// File path: assets/icons/ic_setup.svg
  String get icSetup => 'assets/icons/ic_setup.svg';

  /// File path: assets/icons/ic_support.svg
  String get icSupport => 'assets/icons/ic_support.svg';

  /// File path: assets/icons/ic_support_2.svg
  String get icSupport2 => 'assets/icons/ic_support_2.svg';

  /// File path: assets/icons/ic_support_ai.svg
  String get icSupportAi => 'assets/icons/ic_support_ai.svg';

  /// File path: assets/icons/ic_system_maintenance.svg
  String get icSystemMaintenance => 'assets/icons/ic_system_maintenance.svg';

  /// File path: assets/icons/ic_update_ekyc.svg
  String get icUpdateEkyc => 'assets/icons/ic_update_ekyc.svg';

  /// File path: assets/icons/ic_user.svg
  String get icUser => 'assets/icons/ic_user.svg';

  /// Directory path: assets/icons/mini_app
  $AssetsIconsMiniAppGen get miniApp => const $AssetsIconsMiniAppGen();

  /// List of all assets
  List<String> get values => [
    clipboardalt,
    dialogIconPhoto,
    dialogIconPhotoDark,
    icAddUser,
    icAsset,
    icBond,
    icBtnCamera,
    icCamera,
    icCapture,
    icCaptureCopy,
    icCaptureDialog,
    icChangeMoney,
    icCloseGame,
    icFeatureEdit,
    icFeatureEmpty,
    icHomeAi,
    icInvest,
    icMemberRank,
    icNotiYellow,
    icNotification,
    icOrderConfirm,
    icPartnerConnection,
    icSearch,
    icSec,
    icSecurity,
    icSetCommand,
    icSetup,
    icSupport,
    icSupport2,
    icSupportAi,
    icSystemMaintenance,
    icUpdateEkyc,
    icUser,
  ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/bg_building.png
  AssetGenImage get bgBuilding =>
      const AssetGenImage('assets/images/bg_building.png');

  /// File path: assets/images/bg_gradient.png
  AssetGenImage get bgGradient =>
      const AssetGenImage('assets/images/bg_gradient.png');

  /// File path: assets/images/bg_signature.png
  AssetGenImage get bgSignature =>
      const AssetGenImage('assets/images/bg_signature.png');

  /// File path: assets/images/bg_signature_dark.png
  AssetGenImage get bgSignatureDark =>
      const AssetGenImage('assets/images/bg_signature_dark.png');

  /// File path: assets/images/bg_welcome.png
  AssetGenImage get bgWelcome =>
      const AssetGenImage('assets/images/bg_welcome.png');

  /// File path: assets/images/home_background.svg
  String get homeBackground => 'assets/images/home_background.svg';

  /// File path: assets/images/img_supper_combo.png
  AssetGenImage get imgSupperCombo =>
      const AssetGenImage('assets/images/img_supper_combo.png');

  /// File path: assets/images/img_tc13.jpg
  AssetGenImage get imgTc13 =>
      const AssetGenImage('assets/images/img_tc13.jpg');

  /// File path: assets/images/logo_welcome.png
  AssetGenImage get logoWelcome =>
      const AssetGenImage('assets/images/logo_welcome.png');

  /// List of all assets
  List<dynamic> get values => [
    bgBuilding,
    bgGradient,
    bgSignature,
    bgSignatureDark,
    bgWelcome,
    homeBackground,
    imgSupperCombo,
    imgTc13,
    logoWelcome,
  ];
}

class $AssetsIconsMiniAppGen {
  const $AssetsIconsMiniAppGen();

  /// File path: assets/icons/mini_app/ic_baomat.svg
  String get icBaomat => 'assets/icons/mini_app/ic_baomat.svg';

  /// File path: assets/icons/mini_app/ic_boloccophieu.svg
  String get icBoloccophieu => 'assets/icons/mini_app/ic_boloccophieu.svg';

  /// File path: assets/icons/mini_app/ic_canhbao.svg
  String get icCanhbao => 'assets/icons/mini_app/ic_canhbao.svg';

  /// File path: assets/icons/mini_app/ic_ccquy.svg
  String get icCcquy => 'assets/icons/mini_app/ic_ccquy.svg';

  /// File path: assets/icons/mini_app/ic_chuyekhoanck.svg
  String get icChuyekhoanck => 'assets/icons/mini_app/ic_chuyekhoanck.svg';

  /// File path: assets/icons/mini_app/ic_chuyentien.svg
  String get icChuyentien => 'assets/icons/mini_app/ic_chuyentien.svg';

  /// File path: assets/icons/mini_app/ic_dangkyquyenmua.svg
  String get icDangkyquyenmua => 'assets/icons/mini_app/ic_dangkyquyenmua.svg';

  /// File path: assets/icons/mini_app/ic_danhmuc.svg
  String get icDanhmuc => 'assets/icons/mini_app/ic_danhmuc.svg';

  /// File path: assets/icons/mini_app/ic_datlenhcp.svg
  String get icDatlenhcp => 'assets/icons/mini_app/ic_datlenhcp.svg';

  /// File path: assets/icons/mini_app/ic_datlenhps.svg
  String get icDatlenhps => 'assets/icons/mini_app/ic_datlenhps.svg';

  /// File path: assets/icons/mini_app/ic_dinhvutaichinh.svg
  String get icDinhvutaichinh => 'assets/icons/mini_app/ic_dinhvutaichinh.svg';

  /// File path: assets/icons/mini_app/ic_eport.svg
  String get icEport => 'assets/icons/mini_app/ic_eport.svg';

  /// File path: assets/icons/mini_app/ic_gioithieu.svg
  String get icGioithieu => 'assets/icons/mini_app/ic_gioithieu.svg';

  /// File path: assets/icons/mini_app/ic_hotro.svg
  String get icHotro => 'assets/icons/mini_app/ic_hotro.svg';

  /// File path: assets/icons/mini_app/ic_khuyennghidautu.svg
  String get icKhuyennghidautu =>
      'assets/icons/mini_app/ic_khuyennghidautu.svg';

  /// File path: assets/icons/mini_app/ic_lichsu.svg
  String get icLichsu => 'assets/icons/mini_app/ic_lichsu.svg';

  /// File path: assets/icons/mini_app/ic_lichsukien.svg
  String get icLichsukien => 'assets/icons/mini_app/ic_lichsukien.svg';

  /// File path: assets/icons/mini_app/ic_loyalty.svg
  String get icLoyalty => 'assets/icons/mini_app/ic_loyalty.svg';

  /// File path: assets/icons/mini_app/ic_naptien.svg
  String get icNaptien => 'assets/icons/mini_app/ic_naptien.svg';

  /// File path: assets/icons/mini_app/ic_neoai.svg
  String get icNeoai => 'assets/icons/mini_app/ic_neoai.svg';

  /// File path: assets/icons/mini_app/ic_nopkiquyps.svg
  String get icNopkiquyps => 'assets/icons/mini_app/ic_nopkiquyps.svg';

  /// File path: assets/icons/mini_app/ic_saokeck.svg
  String get icSaokeck => 'assets/icons/mini_app/ic_saokeck.svg';

  /// File path: assets/icons/mini_app/ic_saokekyquyps.svg
  String get icSaokekyquyps => 'assets/icons/mini_app/ic_saokekyquyps.svg';

  /// File path: assets/icons/mini_app/ic_saoketien.svg
  String get icSaoketien => 'assets/icons/mini_app/ic_saoketien.svg';

  /// File path: assets/icons/mini_app/ic_smart_otp.svg
  String get icSmartOtp => 'assets/icons/mini_app/ic_smart_otp.svg';

  /// File path: assets/icons/mini_app/ic_solenh.svg
  String get icSolenh => 'assets/icons/mini_app/ic_solenh.svg';

  /// File path: assets/icons/mini_app/ic_taisan.svg
  String get icTaisan => 'assets/icons/mini_app/ic_taisan.svg';

  /// File path: assets/icons/mini_app/ic_thongtinthitruong.svg
  String get icThongtinthitruong =>
      'assets/icons/mini_app/ic_thongtinthitruong.svg';

  /// File path: assets/icons/mini_app/ic_tichsan.svg
  String get icTichsan => 'assets/icons/mini_app/ic_tichsan.svg';

  /// File path: assets/icons/mini_app/ic_traiphieu.svg
  String get icTraiphieu => 'assets/icons/mini_app/ic_traiphieu.svg';

  /// File path: assets/icons/mini_app/ic_ungtruoctienban.svg
  String get icUngtruoctienban =>
      'assets/icons/mini_app/ic_ungtruoctienban.svg';

  /// File path: assets/icons/mini_app/ic_xacnhan.svg
  String get icXacnhan => 'assets/icons/mini_app/ic_xacnhan.svg';

  /// File path: assets/icons/mini_app/ic_xephang.svg
  String get icXephang => 'assets/icons/mini_app/ic_xephang.svg';

  /// List of all assets
  List<String> get values => [
    icBaomat,
    icBoloccophieu,
    icCanhbao,
    icCcquy,
    icChuyekhoanck,
    icChuyentien,
    icDangkyquyenmua,
    icDanhmuc,
    icDatlenhcp,
    icDatlenhps,
    icDinhvutaichinh,
    icEport,
    icGioithieu,
    icHotro,
    icKhuyennghidautu,
    icLichsu,
    icLichsukien,
    icLoyalty,
    icNaptien,
    icNeoai,
    icNopkiquyps,
    icSaokeck,
    icSaokekyquyps,
    icSaoketien,
    icSmartOtp,
    icSolenh,
    icTaisan,
    icThongtinthitruong,
    icTichsan,
    icTraiphieu,
    icUngtruoctienban,
    icXacnhan,
    icXephang,
  ];
}

class Assets {
  const Assets._();

  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
