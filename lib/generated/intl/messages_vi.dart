// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a vi locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'vi';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "captureProfile": MessageLookupByLibrary.simpleMessage("Chụp ảnh hồ sơ"),
    "captureSignature": MessageLookupByLibrary.simpleMessage("Chụp ảnh chữ ký"),
    "home_actual_margin_ratio": MessageLookupByLibrary.simpleMessage(
      "Tỉ lệ ký quỹ thực tế",
    ),
    "home_add_now": MessageLookupByLibrary.simpleMessage("Bổ sung ngay"),
    "home_agree": MessageLookupByLibrary.simpleMessage("Tôi đồng ý/ I agree"),
    "home_asset": MessageLookupByLibrary.simpleMessage("Tài sản"),
    "home_byChosenBelow": MessageLookupByLibrary.simpleMessage(
      "bằng việc tích chọn dưới đây.",
    ),
    "home_byChosenBelow_en": MessageLookupByLibrary.simpleMessage(
      "by clicking the below box.",
    ),
    "home_close": MessageLookupByLibrary.simpleMessage("Đóng"),
    "home_complete": MessageLookupByLibrary.simpleMessage("Hoàn thành"),
    "home_confirm_logout": MessageLookupByLibrary.simpleMessage(
      "Bạn có chắc chắn muốn đăng xuất khỏi tài khoản?",
    ),
    "home_confirmedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "Xác nhận thành công",
    ),
    "home_contentPermissionDialog": MessageLookupByLibrary.simpleMessage(
      "VPBank Securities cần cấp quyền camera và thư viện ảnh để có thể truy cập và cập nhật ảnh đại diện của Quý khách",
    ),
    "home_contractOTPConfirmButton": MessageLookupByLibrary.simpleMessage(
      "Xác nhận",
    ),
    "home_contractOTPDialogContent": MessageLookupByLibrary.simpleMessage(
      "<div style=\"text-align: center\">VPBankS thông báo cập nhật nội dung <a href=\"https://33ebaeb4-b166-4507-af2e-e29a08919483.usrfiles.com/ugd/33ebae_ced5323105734e4081a128d43c078551.pdf\" style=\"color:#48D597;text-decoration:none\">điều khoản và điều kiện</a> của Hợp đồng mở tài khoản chứng khoán (T&C).<br>Bằng việc nhấn “Xác nhận” bạn đã đọc và đồng ý với điều khoản và điều kiện mới.</div>",
    ),
    "home_contractOTPDialogTitle": MessageLookupByLibrary.simpleMessage(
      "Cập nhật điều khoản hợp đồng",
    ),
    "home_derivatives": MessageLookupByLibrary.simpleMessage("Phái sinh"),
    "home_descMember": MessageLookupByLibrary.simpleMessage(
      "Hạng, điểm thưởng, ưu đãi",
    ),
    "home_desc_customer": MessageLookupByLibrary.simpleMessage(
      "Thông tin khách hàng",
    ),
    "home_downloadFail": MessageLookupByLibrary.simpleMessage(
      "Tải xuống thất bại",
    ),
    "home_downloadSuccess": MessageLookupByLibrary.simpleMessage(
      "Tải xuống thành công",
    ),
    "home_edit": MessageLookupByLibrary.simpleMessage("Tùy chỉnh"),
    "home_favorist_features": MessageLookupByLibrary.simpleMessage(
      "Tính năng ưa thích",
    ),
    "home_features_list": MessageLookupByLibrary.simpleMessage(
      "Danh sách tính năng",
    ),
    "home_find_features": MessageLookupByLibrary.simpleMessage(
      "Tìm kiếm tính năng",
    ),
    "home_generalTermsAndConditions": MessageLookupByLibrary.simpleMessage(
      "Điều khoản và điều kiện chung",
    ),
    "home_generalTermsAndConditionsNd13": MessageLookupByLibrary.simpleMessage(
      "Điều Khoản Và Điều Kiện Chung về bảo vệ và xử lý dữ liệu cá nhân ",
    ),
    "home_generalTermsAndConditionsNd13_en": MessageLookupByLibrary.simpleMessage(
      "General Terms and Conditions on personal data protection and processing ",
    ),
    "home_getSmartOTP": MessageLookupByLibrary.simpleMessage(
      "Lấy mã Smart OTP",
    ),
    "home_get_smart_oTP": MessageLookupByLibrary.simpleMessage(
      "Lấy mã Smart OTP",
    ),
    "home_goToSetting": MessageLookupByLibrary.simpleMessage("Vào cài đặt"),
    "home_infoGeneralTermsAndConditions": MessageLookupByLibrary.simpleMessage(
      "Thông tin điều khoản và điều kiện chung theo Nghị định 13/2023/NĐ-CP",
    ),
    "home_info_customer": MessageLookupByLibrary.simpleMessage(
      "Thông tin khách hàng",
    ),
    "home_introduceFriend": MessageLookupByLibrary.simpleMessage(
      "Giới thiệu bạn mới",
    ),
    "home_introduceFriendAndCustomer": MessageLookupByLibrary.simpleMessage(
      "Giới thiệu bạn bè & khách hàng",
    ),
    "home_invest": MessageLookupByLibrary.simpleMessage("Đầu tư"),
    "home_logout": MessageLookupByLibrary.simpleMessage("Đăng xuất"),
    "home_max_features_failed": MessageLookupByLibrary.simpleMessage(
      "Bạn chỉ được chọn tối đa 8 tính năng",
    ),
    "home_membershipOffer": MessageLookupByLibrary.simpleMessage(
      "Khách hàng thân thiết",
    ),
    "home_min_features_failed": MessageLookupByLibrary.simpleMessage(
      "Bạn cần đặt ít nhất 4 tính năng",
    ),
    "home_missing_signature": MessageLookupByLibrary.simpleMessage(
      "Tài khoản của bạn chưa hoàn thiện do thiếu ảnh Chữ ký.\nMột số tính năng sẽ bị giới hạn",
    ),
    "home_openCamera": MessageLookupByLibrary.simpleMessage(
      "Bật quyền truy cập",
    ),
    "home_partnerConnection": MessageLookupByLibrary.simpleMessage(
      "Kết nối đối tác",
    ),
    "home_property_nav": MessageLookupByLibrary.simpleMessage(
      "Tài sản ròng (NAV)",
    ),
    "home_sec": MessageLookupByLibrary.simpleMessage("Cổ phiếu"),
    "home_security": MessageLookupByLibrary.simpleMessage("Bảo mật"),
    "home_setup": MessageLookupByLibrary.simpleMessage("Cài đặt"),
    "home_stock_derivatives": MessageLookupByLibrary.simpleMessage("Phái sinh"),
    "home_stock_invest": MessageLookupByLibrary.simpleMessage("Đầu tư"),
    "home_stock_other": MessageLookupByLibrary.simpleMessage("Tiện ích khác"),
    "home_stock_utility": MessageLookupByLibrary.simpleMessage(
      "Tiện ích cổ phiếu",
    ),
    "home_support": MessageLookupByLibrary.simpleMessage("Hỗ trợ"),
    "home_takePickture": MessageLookupByLibrary.simpleMessage("Chụp ảnh"),
    "home_takePicktureFromGallery": MessageLookupByLibrary.simpleMessage(
      "Chọn ảnh từ thiết bị",
    ),
    "home_update": MessageLookupByLibrary.simpleMessage("Cập nhật"),
    "home_updateAvtSuccess": MessageLookupByLibrary.simpleMessage(
      "Cập nhật ảnh đại diện thành công",
    ),
    "home_updateEkyc": MessageLookupByLibrary.simpleMessage(
      "VPBankS thông báo Quý khách cần thực hiện cập nhật thông tin căn cước công dân theo quy định tại Công văn số: 4501/UBCK-CNTT",
    ),
    "home_updateNd13": MessageLookupByLibrary.simpleMessage(
      "Để bảo vệ dữ liệu cá nhân của Khách Hàng theo  Nghị định 13/2023/NĐ-CP, Quý Khách vui lòng xác nhận đồng ý với ",
    ),
    "home_updateNd13_en": MessageLookupByLibrary.simpleMessage(
      "To protect your personal data in accordance with Decree No. 13/2023/ND-CP, please kindly confirm your acceptance with the ",
    ),
    "home_updatePersonalInformation": MessageLookupByLibrary.simpleMessage(
      "Cập nhật thông tin cá nhân",
    ),
    "home_update_success": MessageLookupByLibrary.simpleMessage(
      "Cập nhật thành công.",
    ),
    "home_version": MessageLookupByLibrary.simpleMessage("VPBank Securities v"),
    "minimalistInvesting": MessageLookupByLibrary.simpleMessage(
      "Wealth in hands",
    ),
    "next": MessageLookupByLibrary.simpleMessage("Tiếp theo"),
    "orderConfirmNow": MessageLookupByLibrary.simpleMessage("Xác nhận ngay"),
    "orderConfirmWidgetDescription": MessageLookupByLibrary.simpleMessage(
      "Quý khách cần xác nhận các lệnh đặt cổ phiếu của nhân viên chăm sóc",
    ),
    "orderConfirmWidgetTitle": MessageLookupByLibrary.simpleMessage(
      "Giao dịch chưa hoàn tất",
    ),
    "please_take_photo": MessageLookupByLibrary.simpleMessage(
      "Vui lòng chụp ảnh chữ ký của bạn",
    ),
    "register": MessageLookupByLibrary.simpleMessage("Đăng ký tài khoản"),
    "signature": MessageLookupByLibrary.simpleMessage("Chữ ký"),
    "signatureSuccess": MessageLookupByLibrary.simpleMessage(
      "Đã cập nhật chữ ký",
    ),
    "splash_login": MessageLookupByLibrary.simpleMessage("Đăng nhập"),
    "splash_register": MessageLookupByLibrary.simpleMessage("Mở tài khoản"),
    "systemMaintenanceContent": MessageLookupByLibrary.simpleMessage(
      "Hoạt động bảo trì có thể mất vài phút hoặc vài tiếng, chúng tôi xin lỗi về sự bất tiện này.",
    ),
    "systemMaintenanceTitle": MessageLookupByLibrary.simpleMessage(
      "Hệ thống đang bảo trì",
    ),
    "vpbs": MessageLookupByLibrary.simpleMessage("Chứng khoán VPBank"),
    "welcome_to": MessageLookupByLibrary.simpleMessage("Chào mừng bạn đến với"),
  };
}
