import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:new_neo_invest/cubit/home/<USER>/banner_cubit.dart';
import 'package:new_neo_invest/cubit/home/<USER>';
import 'package:new_neo_invest/cubit/home/<USER>/notification_cubit.dart';
import 'package:new_neo_invest/cubit/home/<USER>/home_order_confirm_cubit.dart';
import 'package:new_neo_invest/cubit/home/<USER>/popup_update_cubit.dart';
import 'package:new_neo_invest/cubit/home/<USER>/home_shortcut_cubit.dart';
import 'package:new_neo_invest/cubit/home/<USER>/signature_status_bloc.dart';
import 'package:new_neo_invest/gen/assets.gen.dart';
import 'package:new_neo_invest/screen/drawer/drawer_widget.dart';
import 'package:new_neo_invest/screen/home_page/widgets/banner/banner_view.dart';
import 'package:new_neo_invest/screen/home_page/widgets/dialog/dialog_logout.dart';
import 'package:new_neo_invest/screen/home_page/widgets/header/home_info_header_view.dart';
import 'package:new_neo_invest/screen/home_page/widgets/home_order_confirm/home_order_confirm_view.dart';
import 'package:new_neo_invest/screen/home_page/widgets/shortcut/home_shortcut_widget.dart';
import 'package:new_neo_invest/screen/home_page/widgets/signature_status/signature_status_widget.dart';
import 'package:new_neo_invest/screen/home_page/widgets/tabbar/home_navigationbar_widget.dart';
import 'package:new_neo_invest/screen/popup_update/popup_update.dart';
import 'package:vp_assets/cubit/eod/eod_cubit.dart';
import 'package:vp_assets/vp_assets.dart';
import 'package:vp_common/widget/loading/loading_builder.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_fund/screen/home_app/fund_market_view.dart';
import 'package:vp_loyalty/cubit/loyalty/customer_care/rank_point_bloc.dart';
import 'package:vp_loyalty/vp_loyalty.dart';
import 'package:vp_price_board/model/enum/home_stock_category.dart';
import 'package:vp_price_board/vp_price_board.dart';
import 'package:vp_stock_common/vp_stock_common.dart';
import 'package:vp_utility/screen/recommendation_home/inv_rc_view.dart';

class HomePageScreen extends StatefulWidget {
  const HomePageScreen({super.key});

  @override
  State<HomePageScreen> createState() => _HomePageScreenState();
}

class _HomePageScreenState extends State<HomePageScreen>
    with HomeNavigator, PopupUpdate {
  final scrollController = ScrollController();
  final _homeCubit = HomeCubit()..init();
  final _checkLoyaltyAccountCubit = CheckLoyaltyAccountCubit();
  final _scaffoldKey = GlobalKey<ScaffoldState>();
  final rankPointBloc = RankPointBloc()..getUserRankPoint();
  final _blocSignature = SignatureStatusBloc();
  final popupUpdateCubit = PopupUpdateCubit();
  final _orderConfirmCubit = HomeOrderConfirmCubit();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      popupUpdateCubit.init();
    });
  }

  @override
  void dispose() {
    _homeCubit.close();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
        providers: [
          BlocProvider.value(value: _homeCubit),
          BlocProvider(create: (_) => AssetCubit()..fetchData()),
          BlocProvider(
            create: (_) => GetIt.instance<EODCubit>()..getEodStatus(),
          ),
          BlocProvider(
              create: (_) => NotificationCubit()..getTotalUnReadNotification()),
          BlocProvider(create: (_) => BannerCubit()..init()),
          BlocProvider(create: (_) => HomeShortcutCubit()..init()),
          BlocProvider(create: (_) => _checkLoyaltyAccountCubit),
          BlocProvider(create: (_) => rankPointBloc),
          BlocProvider(create: (_) => popupUpdateCubit),
          BlocProvider(create: (_) => _orderConfirmCubit..init()),

          // BlocProvider(create: (_) => FundHomeCubit()..onLoadData()),
        ],
        child: LoadingBuilders<CheckLoyaltyAccountCubit>(
            child: Scaffold(
          key: _scaffoldKey,
          drawer: DrawerWidget(
            onToggleDrawer: toggleDrawer,
            confirmLogout: () async {
              showDialogConfirmLogout(context);
              //   final value = await showLogoutDialog();

              //   if (value == true) _bloc.logout();
            },
            homeCubit: _homeCubit,
            // homeOrderConfirmCubit: _orderConfirmBloc,
          ),
          body: BlocListener<PopupUpdateCubit, PopupUpdateState>(
            listener: (context, state) {
              if (state is UpdateTC13State) {
                showPopupTC13(
                  verifyTC13: popupUpdateCubit.verifyTC13,
                  getContractTC13: popupUpdateCubit.getContractTC13,
                );
              } else if (state is Home4501State) {
                showPopupEKYC(state.needChangeIdMode ?? 0);
              } else if (state is ResignContract) {
                showUpdateContractDialog(popupUpdateCubit.verifyContractOTP);
              }
            },
            child: ColoredBox(
              color: vpColor.backgroundElevationMinus1,
              child: Stack(
                children: [
                  BlocBuilder<HomeCubit, HomeState>(
                    buildWhen: (previous, current) =>
                        previous.isBackgroundHome != current.isBackgroundHome,
                    builder: (context, state) {
                      if (state.isBackgroundHome) {
                        return SvgPicture.asset(
                          Assets.images.homeBackground,
                          width: double.infinity,
                          alignment: Alignment.topCenter,
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                  SingleChildScrollView(
                    controller: scrollController,
                    physics: const ClampingScrollPhysics(),
                    child: Column(
                      children: [
                        const AssetComponentView(),
                        const HomeShortcutWidget(),
                        //banner
                        const BannerView(
                            //   onResult: (value) async {
                            // if (value ?? false) {
                            //   await _orderConfirmBloc.getTokenGame();
                            //   // AppTracking.instance.logEvent(
                            //   //   name:
                            //   //       AppTrackingEvent.loginHomeAppBannerGameClick,
                            //   //   type: TrackingType.appsflyer,
                            //   // );
                            //   if (_orderConfirmBloc.enTokenInGame != null &&
                            //       _orderConfirmBloc.enTokenInGame != "") {
                            //     PlatformGameBirthday2025().initGameBirthday2025(
                            //       encryptionToken:
                            //           _orderConfirmBloc.enTokenInGame ?? "",
                            //       eventName: AppTrackingEvent
                            //           .loginHomeAppBannerGameClick,
                            //     );
                            //   } else {
                            //     showCustomSnackBar(
                            //         context, "Có lỗi xảy ra. Vui lòng thử lại",
                            //         isSuccess: false);
                            //   }
                            // }
                            //  },
                            ),
                        SignatureStatusWidget(
                          bloc: _blocSignature,
                        ),
                        const HomeOrderConfirmView(),
                        //     SupperComboWidget(),
                        //    stm.StockMarketView(),
                        const SizedBox(
                          height: 4,
                        ),
                        //     StockMarketView(isHome: true),
                        const SizedBox(
                          height: 4,
                        ),
                        // //banner
                        // // BannerView(),
                        //    FundMarketView(),

                        HomeStockView(
                          onTitleTap: (category) {
                            final index = switch (category) {
                              HomeStockCategory.holding => 1,
                              HomeStockCategory.suggestion => 0,
                              HomeStockCategory.topGainers => 0,
                              HomeStockCategory.topLosers => 0,
                            };

                            navigateToStockHome(
                              context: context,
                              args: StockHomeArgs(initialPage: index),
                            );
                          },
                        ),

                        const SizedBox(
                          height: 8,
                        ),

                        MarketIndexList.simple(),

                        // //bond market,
                        // // BondMarketView(key: bondKey),
                        //   InvRecommendationView(),
                        const SizedBox(
                          height: 8,
                        ),
                        const FundMarketView(),
                        const SizedBox(
                          height: 8,
                        ),
                        BlocBuilder<PopupUpdateCubit, PopupUpdateState>(
                          builder: (context, state) {
                            if (state.isInitial) {
                              return const SizedBox.shrink();
                            }
                            return const InvRecommendationView();
                          },
                        ),
                        const SizedBox(
                          height: 4,
                        ),
                        //asset statistic
                        // const AssetStatisticView(),
                      ],
                    ),
                  ),
                  HeaderHome(
                    scrollController: scrollController,
                    onToggleDrawer: toggleDrawer,
                  ),
                  const Align(
                      alignment: Alignment.bottomCenter, child: EODView()),
                ],
              ),
            ),
          ),
        )));

    // return const Placeholder();
  }

  Future<void> toggleDrawer() async {
    if (_scaffoldKey.currentState!.isDrawerOpen) {
      _scaffoldKey.currentState!.closeDrawer();
    } else {
      _scaffoldKey.currentState!.openDrawer();
    }
  }
}
