import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:new_neo_invest/cubit/home/<USER>/home_order_confirm_cubit.dart';
import 'package:new_neo_invest/gen/assets.gen.dart';
import 'package:new_neo_invest/generated/l10n.dart';
import 'package:new_neo_invest/router/main_router.dart';
import 'package:vp_core/vp_core.dart';

class HomeOrderConfirmView extends StatelessWidget {
  const HomeOrderConfirmView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<HomeOrderConfirmCubit, HomeOrderConfirmState, int>(
      selector: (state) => state.orderConfirmCount,
      builder: (_, orderConfirmCount) {
        if (orderConfirmCount <= 0) return const SizedBox.shrink();

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: vpColor.backgroundElevation0,
            boxShadow: const [
              BoxShadow(
                color: Color(0x1E2A3346),
                blurRadius: 32,
                offset: Offset(0, 16),
              ),
              BoxShadow(
                color: Color(0x0A2A3346),
                blurRadius: 32,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              ListTile(
                minVerticalPadding: 0,
                contentPadding: const EdgeInsets.all(16),
                leading: SvgPicture.asset(
                  Assets.icons.icOrderConfirm,
                ),
                onTap: () => context.push(VPMainRouter.orderConfirm.routeName),
                title: Text(
                  VPNeoLocalize.current.orderConfirmWidgetTitle,
                  style: vpTextStyle.subtitle14.copyColor(vpColor.textPrimary),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    const SizedBox(
                      height: 4,
                    ),
                    Text(
                      VPNeoLocalize.current.orderConfirmWidgetDescription,
                      style: vpTextStyle.captionMedium?.copyWith(
                        color: vpColor.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      VPNeoLocalize.current.orderConfirmNow,
                      style: vpTextStyle.captionMedium
                          .copyColor(vpColor.textBrand),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
