import 'package:flutter/material.dart';
import 'package:new_neo_invest/cubit/home/<USER>/popup_update_cubit.dart';
import 'package:new_neo_invest/gen/assets.gen.dart';
import 'package:new_neo_invest/main_app_module.dart';
import 'package:vp_core/vp_core.dart';

class SupperComboWidget extends StatefulWidget {
  const SupperComboWidget({super.key});

  @override
  State<SupperComboWidget> createState() => _SupperComboWidgetState();
}

class _SupperComboWidgetState extends State<SupperComboWidget> {
  bool _showPopup = false;

  @override
  Widget build(BuildContext context) {
    return BlocListener<PopupUpdateCubit, PopupUpdateState>(
      listener: (context, state) {
        if (state is HomePopupSupperComboState) {
          setState(() {
            _showPopup = state.isShowByFirebase;
          });
        }
      },
      child: _showPopup
          ? Container(
              margin: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: vpColor.backgroundElevation0,
                boxShadow: const [
                  BoxShadow(
                    color: Color(0x1E2A3346),
                    blurRadius: 32,
                    offset: Offset(0, 16),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: Color(0x0A2A3346),
                    blurRadius: 32,
                    offset: Offset(0, 4),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: GestureDetector(
                onTap: () {
                  // AppTracking.instance.logEvent(
                  //   name: AppTrackingEvent.bannerSupperCombo,
                  //   type: TrackingType.appsflyer,
                  // );
                  context.push(MainRouter.supperCombo.routeName);
                },
                child: Assets.images.imgSupperCombo.image(),
              ),
            )
          : const SizedBox(),
    );
  }
}
