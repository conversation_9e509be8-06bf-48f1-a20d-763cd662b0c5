import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:new_neo_invest/generated/l10n.dart';
import 'package:printing/printing.dart';
import 'package:vp_common/vp_common.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_core/utils/show_error.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/generated/assets.gen.dart';

class FundContract {
  FundContract(this.url, this.requestId, this.companyId, this.productCode);

  String url = '';
  String requestId = '';
  String companyId = '';
  String productCode = '';
}

class ContractScreen extends StatefulWidget {
  const ContractScreen({required this.fundContract, super.key});

  final FundContract fundContract;

  @override
  State<ContractScreen> createState() => _ContractScreenState();
}

class _ContractScreenState extends State<ContractScreen> {
  Future<Uint8List?> getContractData(BuildContext context) async {
    try {
      if (DownloadUtils().checkUrlValid(widget.fundContract.url)) {
        var data = await DownloadUtils().getUrlContent(widget.fundContract.url);
        if (data != null) {
          return data;
        }
      }
      return Future.error('');
    } catch (e) {
      return Future.error('');
    }
  }

  void downloadContract(BuildContext context) async {
    final randomId = AppHelper().genXRequestID();
    final String tag = 'download_contract_$randomId';
    EasyDebounce.debounce(tag, const Duration(milliseconds: 1000), () async {
      try {
        showSnackBar(context, VPCommonLocalize.current.downloading,
            asset: VpTradingAssets.icons.icDownload.path,
            colorAsset: themeData.black,
            package: 'vp_trading');
        final String defaultFileName = 'contract$randomId';
        final downloadResult = await DownloadFileManager.instance.downloadFile(
          fileName: defaultFileName,
          extension: FileExtension.pdf,
          url: widget.fundContract.url,
        );

        showMessage(
          downloadResult != null
              ? VPNeoLocalize.current.home_downloadSuccess
              : VPNeoLocalize.current.home_downloadFail,
          isSuccess: downloadResult != null,
        );
      } catch (e) {
        showErrorMessage(
          VPNeoLocalize.current.home_downloadFail,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            HeaderWidget(
              showSubTitle: false,
              title: VPNeoLocalize.current.home_generalTermsAndConditions,
              titleStyle:
                  vpTextStyle.headineBold6?.copyWith(color: themeData.black),
              back: false,
            ),
            const SizedBox(
              height: 16,
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Text(
                      VPNeoLocalize.current.home_infoGeneralTermsAndConditions,
                      style: vpTextStyle.body14
                          ?.copyWith(color: themeData.gray900),
                    ),
                  ),
                  const SizedBox(width: 16),
                  InkWell(
                    onTap: () {
                      downloadContract(context);
                    },
                    child: SvgPicture.asset(
                      VpTradingAssets.icons.icDownload.path,
                      width: 24,
                      height: 24,
                      colorFilter:
                          ColorFilter.mode(themeData.gray900, BlendMode.srcIn),
                      package: 'vp_trading',
                    ),
                  )
                ],
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const DividerWidget(),
                  Expanded(
                    child: FutureBuilder<Uint8List?>(
                      future: getContractData(context),
                      builder: (cxt, snapshot) {
                        if (snapshot.hasData && snapshot.data != null) {
                          return PdfPreview(
                            build: (format) {
                              return snapshot.data!;
                            },
                            loadingWidget: const VPBankLoading(),
                            padding: EdgeInsets.zero,
                            useActions: false,
                            previewPageMargin: EdgeInsets.zero,
                            scrollViewDecoration: const BoxDecoration(
                              color: Colors.white,
                            ),
                            pdfPreviewPageDecoration: const BoxDecoration(
                              color: Colors.white,
                            ),
                            allowSharing: false,
                            allowPrinting: false,
                            canChangePageFormat: false,
                          );
                        } else if (snapshot.hasError) {
                          final message = snapshot.error.toString();
                          return Center(
                            child: Text(
                              message.isEmpty
                                  ? VPCommonLocalize.current.err_unknown
                                  : message,
                            ),
                          );
                        }
                        return const Center(child: VPBankLoading());
                      },
                    ),
                  ),
                  const DividerWidget(),
                  SizedBox(
                    width: double.infinity,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: VpsButton.secondarySmall(
                        title: VPNeoLocalize.current.home_close,
                        onPressed: context.pop,
                      ),
                    ),
                  ),
                  DividerWidget(
                    color: themeData.gray300,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
