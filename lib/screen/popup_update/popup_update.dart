import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:go_router/go_router.dart';
import 'package:new_neo_invest/gen/assets.gen.dart';
import 'package:new_neo_invest/generated/l10n.dart';
import 'package:vp_auth/router/account_router.dart';
import 'package:vp_auth/screen/sign_up/sign_up_page/helper/data/sign_up_data.dart';
import 'package:vp_common/utils/navigation_service.dart';
import 'package:vp_core/theme/bloc/theme_cubit.dart';
import 'package:vp_design_system/vp_design_system.dart';

mixin PopupUpdate {
  Future<void> showPopupEKYC(int needChangeIdMode) async {
    if (needChangeIdMode == 0) return;
    if (needChangeIdMode == 1) {
      await VPPopup.outlineAndPrimaryButton(
        title: VPNeoLocalize.current.home_updatePersonalInformation,
        content: VPNeoLocalize.current.home_updateEkyc,
      )
          .copyWith(
            icon: Assets.icons.icUpdateEkyc,
          )
          .copyWith(
            button: VpsButton.secondarySmall(
              title: VPNeoLocalize.current.home_close,
              onPressed: getContext.pop,
            ),
          )
          .copyWith(
              button: VpsButton.primarySmall(
            title: VPNeoLocalize.current.home_update,
            onPressed: () {
              SignUpData().clear();
              getContext
                ..pop()
                ..push(AccountRouter.uploadOcrPage.routeName);
            },
          ))
          .showDialog(getContext);
    } else {
      await VPPopup.oneButton(
              title: VPNeoLocalize.current.home_updatePersonalInformation,
              content: VPNeoLocalize.current.home_updateEkyc)
          .copyWith(
            icon: Assets.icons.icUpdateEkyc,
          )
          .copyWith(
            button: VpsButton.primarySmall(
              title: VPNeoLocalize.current.home_update,
              onPressed: () {
                SignUpData().clear();
                getContext
                  ..pop()
                  ..push(AccountRouter.uploadOcrPage.routeName);
              },
            ),
          )
          .showDialog(getContext);
    }
  }

  Future<void> showPopupTC13({
    required VoidCallback verifyTC13,
    required VoidCallback getContractTC13,
  }) async {
    final isAcceptTC13Notifier = ValueNotifier<bool>(false);
    await VPPopup(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        // Giảm kích thước tối đa của Column
        children: [
          Image.asset(
            Assets.images.imgTc13.path,
            fit: BoxFit.fitHeight,
          ),
          const SizedBox(height: 16),
          RichText(
              text: TextSpan(
                  text: VPNeoLocalize.current.home_updateNd13,
                  style: vpTextStyle.body14?.copyWith(color: themeData.black),
                  children: [
                TextSpan(
                  recognizer: TapGestureRecognizer()..onTap = getContractTC13,
                  text:
                      VPNeoLocalize.current.home_generalTermsAndConditionsNd13,
                  style: vpTextStyle.body14?.copyWith(color: themeData.primary),
                ),
                TextSpan(
                  text: VPNeoLocalize.current.home_byChosenBelow,
                  style: vpTextStyle.body14?.copyWith(color: themeData.black),
                ),
              ])),
          const SizedBox(
            height: 16,
          ),
          RichText(
            text: TextSpan(
              text: VPNeoLocalize.current.home_updateNd13_en,
              style: vpTextStyle.body14?.copyWith(color: themeData.black),
              children: [
                TextSpan(
                  recognizer: TapGestureRecognizer()..onTap = getContractTC13,
                  text: VPNeoLocalize
                      .current.home_generalTermsAndConditionsNd13_en,
                  style: vpTextStyle.body14?.copyWith(color: themeData.primary),
                ),
                TextSpan(
                  text: VPNeoLocalize.current.home_byChosenBelow_en,
                  style: vpTextStyle.body14?.copyWith(color: themeData.black),
                ),
              ],
            ),
          ),
          const SizedBox(
            height: 8,
          ),
          Row(
            children: [
              SizedBox(
                height: 24,
                width: 24,
                child: ValueListenableBuilder<bool>(
                  valueListenable: isAcceptTC13Notifier,
                  builder: (_, isAcceptTC13, __) {
                    return Checkbox(
                      activeColor: themeData.primary,
                      value: isAcceptTC13,
                      onChanged: (value) {
                        isAcceptTC13Notifier.value = value ?? false;
                      },
                    );
                  },
                ),
              ),
              const SizedBox(
                width: 8,
              ),
              Expanded(
                child: Text(
                  VPNeoLocalize.current.home_agree,
                  style: vpTextStyle.body16?.copyWith(color: themeData.black),
                ),
              ),
            ],
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      contentPadding: const EdgeInsets.only(bottom: 16),
      buttons: [
        VpsButton.secondarySmall(
          title: VPNeoLocalize.current.home_close,
          onPressed: getContext.pop,
        ),
        ValueListenableBuilder<bool>(
          valueListenable: isAcceptTC13Notifier,
          builder: (context, isAcceptTC13, ___) {
            return VpsButton.primarySmall(
              title: VPNeoLocalize.current.home_complete,
              disabled: !isAcceptTC13,
              onPressed: () {
                if (!isAcceptTC13) return;
                verifyTC13();
                context.pop();
              },
            );
          },
        ),
      ],
    ).showDialog(getContext);
    isAcceptTC13Notifier.dispose();
  }

  Future<void> showUpdateContractDialog(VoidCallback callback) {
    return VPPopup(
      Column(
        children: [
          const SizedBox(height: 16),
          Text(
            VPNeoLocalize.current.home_contractOTPDialogTitle,
            style:
                vpTextStyle.headineBold6?.copyWith(color: vpColor.textPrimary),
          ),
          const SizedBox(height: 8),
          HtmlWidget(
            VPNeoLocalize.current.home_contractOTPDialogContent,
            textStyle: vpTextStyle.body14,
          )
        ],
      ),
      padding: const EdgeInsets.all(20),
      contentPadding: const EdgeInsets.only(bottom: 16),
    )
        .copyWith(
            icon: SvgPicture.asset(Assets.icons.icUpdateEkyc),
            button: VpsButton.primarySmall(
              title: VPNeoLocalize.current.home_contractOTPConfirmButton,
              onPressed: () {
                getContext.pop();
                callback();
              },
            ))
        .showDialog(getContext);
  }
}
