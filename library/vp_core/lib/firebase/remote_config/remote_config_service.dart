import 'dart:convert';
import 'dart:math';

import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:vp_common/vp_common.dart';

class RemoteConfigService {
  late FirebaseRemoteConfig _remoteConfig;

  static final RemoteConfigService _singleton = RemoteConfigService._internal();

  factory RemoteConfigService() {
    return _singleton;
  }

  RemoteConfigService._internal() {
    _remoteConfig = FirebaseRemoteConfig.instance;
    _remoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 3),
        minimumFetchInterval: const Duration(minutes: 5),
      ),
    );
//    _remoteConfig.setDefaults({'show_button_order_confirm_late': true});
  }

  Future<void> initConfig() async {
    _fetchConfig();
  }


  // Fetching, caching, and activating remote config
  void _fetchConfig() async {
    try {
      await _remoteConfig.fetchAndActivate();
    } catch (_, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
    }
  }

  bool getBool(String key) {
    try {
      return _remoteConfig.getBool(key);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      return false;
    }
  }

    Future<bool> getShowWealth() async {
    try {
      bool showWealth = _remoteConfig.getBool('show_wealth');

      return showWealth;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      return false;
    }
  }


  // String get _envKeyPrefix {
  //   final stockConfigs = sl.get<StockBuildConfigs>();

  //   bool isUat = stockConfigs.env == Envirement.uat;

  //   return isUat ? 'uat_' : '';
  // }

  // Future<bool> needUpdateVersion() async {
  //   try {
  //     final PackageInfo info = await PackageInfo.fromPlatform();
  //     double currentVersion =
  //         double.parse(info.version.trim().replaceAll(".", ""));

  //     String forceUpdateCurrentVersionKey =
  //         _remoteConfig.getString('${_envKeyPrefix}force_update_current_version');
  //     double newVersion =
  //         double.parse(forceUpdateCurrentVersionKey.trim().replaceAll(".", ""));

  //     return newVersion > currentVersion;
  //   } catch (exception) {
  //     return false;
  //   }
  // }

  // Future<String> getSuggestionStocks() async {
  //   try {
  //     String suggestionStocks =
  //         _remoteConfig.getString('home_stock_suggestion');

  //     return suggestionStocks;
  //   } catch (e, stackTrace) {
  //     debugPrintStack(stackTrace: stackTrace);
  //     return 'HPG,SSI,TCB,VIC,VPB';
  //   }
  // }

  Future<String> getRemoteConfigStringValue(String key) async {
    try {
      String value = _remoteConfig.getString(key);

      return value;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      return '';
    }
  }

  // BannerModel? getBannerHome() {
  //   try {
  //     final rawData = _remoteConfig.getAll()['${_envKeyPrefix}banner']?.asString();

  //     if (rawData.isNullOrEmpty) return null;

  //     return BannerModel.fromJson(jsonDecode(rawData!));
  //   } catch (e, stackTrace) {
  //     debugPrintStack(stackTrace: stackTrace);
  //     return null;
  //   }
  // }

  bool showBtOrderConfirmLate() {
    try {
      final show = _remoteConfig.getBool('show_button_order_confirm_late');

      return show;
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      return false;
    }
  }

  // Map<String, dynamic>? getPartnerConfig() {
  //   try {
  //     final rawData =
  //         _remoteConfig.getAll()['open_partner_account_config']?.asString();

  //     return jsonDecode(rawData!) as Map<String, dynamic>?;
  //   } catch (e, stackTrace) {
  //     debugPrintStack(stackTrace: stackTrace);
  //     return null;
  //   }
  // }

  Future<T?> remoteConfigValue<T>(
      {required String key,
      required T Function(Object? json) fromJsonT}) async {
    try {
      final rawData = _remoteConfig.getAll()[key]?.asString();
      if (rawData.isNullOrEmpty) return null;
      var response = jsonDecode(rawData!);
      final _random = Random();
      var dataList = (response['data'] as List);
      var element = dataList[_random.nextInt(dataList.length)];
      return _$nullableGenericFromJson(
          jsonDecode(jsonEncode((element))), fromJsonT);
    } catch (e, stackTrace) {
      debugPrintStack(stackTrace: stackTrace);
      return null;
    }
  }

  T? _$nullableGenericFromJson<T>(
    Object? input,
    T Function(Object? json) fromJson,
  ) =>
      input == null ? null : fromJson(input);

  // Future<BackgroundEntity?> getBackground() async {
  //   try {
  //     final rawData = _remoteConfig.getAll()['background']?.asString();
  //     if (rawData.isNullOrEmpty) return null;
  //     var response = jsonDecode(rawData!);
  //     var dataList = (response['data'] as List);
  //     var element = dataList[0];
  //     return BackgroundEntity.fromJson(element);
  //   } catch (e, stackTrace) {
  //     debugPrintStack(stackTrace: stackTrace);
  //     return null;
  //   }
  // }

  // Future<String> getContractTC13() async {
  //   try {
  //     String linkContract = _remoteConfig.getString('contract_tc13');

  //     return linkContract;
  //   } catch (e, stackTrace) {
  //     debugPrintStack(stackTrace: stackTrace);
  //     return '';
  //   }
  // }

  // Future<SupperComboEntity?> getSupperCombo() async {
  //   try {
  //     final rawData = _remoteConfig.getAll()['supper_combo']?.asString();
  //     if (rawData.isNullOrEmpty) return null;
  //     var response = jsonDecode(rawData!);
  //     return SupperComboEntity.fromJson(response['data']);
  //   } catch (e, stackTrace) {
  //     debugPrintStack(stackTrace: stackTrace);
  //     return null;
  //   }
  // }
}
